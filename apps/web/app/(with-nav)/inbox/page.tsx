'use client'

import { useState, useMemo, CSSProperties, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { createSecureBrowserClient } from '@/lib/session-security'
import { useOrgEntitySelection } from '@/hooks/useOrgEntitySelection'
import { useAuth } from '@/contexts/AuthContext'
import { ZenIcons } from '@/components/ZenIcons'
import { useInboxDocuments } from '@/hooks/useInboxDocuments'


const BFF_BASE_URL = process.env.NEXT_PUBLIC_BFF_URL || 'http://localhost:4000'



// Zen UI Theme - Following the style guide exactly
const zenTheme = {
  // Primary Colors - CORRECTED to match style guide
  bg: '#FBFAF5', // soft cream background (was wrong before)
  surface: '#FFFFFC', // pure white for cards
  primaryText: '#1a1a1a', // near black
  secondaryText: '#6b7280', // warm gray
  subtleText: '#9ca3af', // light gray

  // Borders and Surfaces
  border: '#f3f4f6', // subtle border
  borderHover: '#e5e7eb', // slightly darker on hover

  // Accent Colors (use sparingly)
  success: '#10b981', // soft green
  warning: '#f59e0b', // warm amber
  error: '#ef4444', // soft red
  primaryAction: '#3b82f6', // calm blue (not black for primary action)

  // Shadows and Effects
  shadow: '0 1px 3px 0 rgba(0, 0, 0, 0.05)', // very subtle
  shadowHover: '0 1px 3px 0 rgba(0, 0, 0, 0.1)', // slightly more on hover
}

// Zen UI Style functions
const getPageStyle = (theme: typeof zenTheme): CSSProperties => ({
  color: theme.primaryText,
  fontFamily:
    "'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', system-ui, sans-serif",
  lineHeight: 1.5, // improved readability
})

const getCardStyle = (theme: typeof zenTheme): CSSProperties => ({
  background: theme.surface,
  border: `1px solid ${theme.border}`,
  borderRadius: '8px', // reduced for cleaner look
  boxShadow: theme.shadow,
  transition: 'all 0.15s ease', // subtle interaction
})

const getButtonStyle = (
  theme: typeof zenTheme,
  variant: 'primary' | 'secondary' = 'secondary'
): CSSProperties => ({
  padding: '10px 20px', // following style guide
  border: variant === 'primary' ? 'none' : `1px solid ${theme.border}`,
  borderRadius: '6px', // reduced for cleaner look
  background: variant === 'primary' ? '#1a1a1a' : 'transparent', // confident near-black for buttons
  color: variant === 'primary' ? '#FFFFFF' : theme.secondaryText,
  fontSize: '14px',
  fontWeight: 500,
  cursor: 'pointer',
  transition: 'all 0.15s ease', // faster, more subtle
})

const getSelectStyle = (theme: typeof zenTheme): CSSProperties => ({
  padding: '10px 12px', // slightly increased
  border: `1px solid ${theme.border}`,
  borderRadius: '6px', // reduced for consistency
  background: theme.surface,
  color: theme.primaryText,
  fontSize: '14px',
  outline: 'none',
  transition: 'all 0.15s ease',
})

export default function InboxPage() {
  const router = useRouter()
  const supabase = useMemo(() => createSecureBrowserClient(), [])
  const { user } = useAuth()
  const {
    currentEntity,
    entities,
    isValid,
    isSME,
    isFirm,
    shouldShowEntitySelector,
    userContext
  } = useOrgEntitySelection()

  const [showUpload, setShowUpload] = useState(false)
  const [file, setFile] = useState<File | null>(null)
  const [selectedEntityId, setSelectedEntityId] = useState<string | null>(
    currentEntity?.entity_id ? String(currentEntity.entity_id) : null
  )

  // Fetch inbox documents
  const { documents, pagination, loading: documentsLoading, error: documentsError, refreshDocuments, updateDocument } = useInboxDocuments()

  // Helper functions for document display
  const getDocumentType = (mimeType: string): string => {
    if (mimeType.includes('pdf')) return 'PDF'
    if (mimeType.includes('image')) return 'Image'
    if (mimeType.includes('text')) return 'Text'
    return 'Document'
  }

  const getDocumentDescription = (doc: any): string => {
    if (doc.supplier_name && doc.invoice_number) {
      return `${doc.supplier_name} - Invoice #${doc.invoice_number}`
    }
    if (doc.supplier_name) {
      return `${doc.supplier_name} - Document`
    }
    if (doc.invoice_number) {
      return `Invoice #${doc.invoice_number}`
    }
    // Fallback to filename from path
    const filename = doc.path.split('/').pop() || 'Unknown Document'
    return filename.replace(/\.[^/.]+$/, '') // Remove extension
  }

  const getStatusDisplay = (status: string) => {
    switch (status) {
      case 'uploaded':
        return { text: 'Unprocessed', color: theme.warning }
      case 'extracted':
        return { text: 'Requires Review', color: '#3b82f6' }
      case 'suggested':
        return { text: 'Ready for Review', color: '#8b5cf6' }
      case 'confirmed':
        return { text: 'Processed', color: theme.success }
      default:
        return { text: status, color: theme.secondaryText }
    }
  }

  const formatDate = (dateString: string): string => {
    try {
      return new Date(dateString).toLocaleDateString('en-GB')
    } catch {
      return dateString
    }
  }

  const formatAmount = (amount?: string): string => {
    if (!amount) return ''
    try {
      const num = parseFloat(amount)
      return `€${num.toFixed(2)}`
    } catch {
      return amount
    }
  }
  const [uploading, setUploading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [success, setSuccess] = useState<string | null>(null)
  const [processingDocuments, setProcessingDocuments] = useState<Set<number>>(new Set())
  const [deletingDocuments, setDeletingDocuments] = useState<Set<number>>(new Set())
  const [viewingDocuments, setViewingDocuments] = useState<Set<number>>(new Set())


  // Use Zen UI theme
  const theme = zenTheme

  // Derived destination for SMEs for display purposes
  const smeDestinationName: string | null = isSME
    ? (
        currentEntity?.entity_name ||
        userContext?.entities?.[0]?.name ||
        (entities.length === 1 ? entities[0].entity_name : null) ||
        null
      )
    : null

  const onOpenUpload = () => {
    setError(null)
    setSuccess(null)
    setFile(null)
    // Default the selection smartly depending on context
    //  - Firms: preselect the current entity if any
    //  - Otherwise fall back to a single available entity if exactly one exists
    const defaultEntityId =
      currentEntity?.entity_id ??
      (userContext?.entities?.length === 1
        ? userContext.entities[0].id
        : entities.length === 1
          ? entities[0].entity_id
          : null)
    setSelectedEntityId(defaultEntityId ? String(defaultEntityId) : null)

    // Debug logging
    console.log('Upload modal opening with:', {
      isSME,
      isFirm,
      shouldShowEntitySelector,
      isValid,
      currentEntity: currentEntity?.entity_name,
      entities: entities.length
    })

    setShowUpload(true)
  }

  const sanitizeName = (name: string) =>
    name.replace(/[^a-zA-Z0-9._-]/g, '-').toLowerCase()

  // Document status helper functions
  const canBeReviewed = (status: string) => ['extracted', 'suggested'].includes(status)
  const canBeProcessed = (status: string) => status === 'uploaded'
  const isProcessed = (status: string) => ['confirmed', 'posted', 'exported'].includes(status)



  // Document action handlers
  const handleViewDocument = async (doc: any) => {
    try {
      setError(null)
      setViewingDocuments(prev => new Set(prev).add(doc.id))

      if (!user) {
        setError('You must be signed in to view documents')
        return
      }

      // Get download URL from BFF
      const { data: sessionData } = await supabase.auth.getSession()
      const token = sessionData.session?.access_token

      const response = await fetch(
        `${BFF_BASE_URL}/documents/${doc.id}/download`,
        {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
            'X-Internal-Key': process.env.NEXT_PUBLIC_BFF_INTERNAL_KEY || '',
            ...(token ? { Authorization: `Bearer ${token}` } : {}),
          },
        }
      )

      if (!response.ok) {
        const errorData = await response.json()
        setError(errorData.error || 'Failed to get document URL')
        return
      }

      const result = await response.json()
      if (result.success && result.data?.download_url) {
        // Open document in new tab
        window.open(result.data.download_url, '_blank')
      } else {
        setError('Failed to get document URL')
      }
    } catch (err) {
      console.error('Error viewing document:', err)
      setError('Failed to view document')
    } finally {
      setViewingDocuments(prev => {
        const newSet = new Set(prev)
        newSet.delete(doc.id)
        return newSet
      })
    }
  }

  const handleProcessDocument = async (doc: any) => {
    console.log('handleProcessDocument called for document:', doc.id, 'status:', doc.status)

    // Route documents based on status
    if (canBeReviewed(doc.status)) {
      // Documents with extracted/suggested status should go to review page
      router.push(`/review/${doc.id}`)
      return
    }

    if (isProcessed(doc.status)) {
      setError('Document has already been processed')
      return
    }

    if (!canBeProcessed(doc.status)) {
      setError(`Cannot process document with status: ${doc.status}`)
      return
    }

    // Process unprocessed documents (uploaded status)
    try {
      setError(null)
      setSuccess(null)
      setProcessingDocuments(prev => new Set(prev).add(doc.id))

      if (!user) {
        setError('You must be signed in to process documents')
        return
      }

      // Call BFF to process document
      const { data: sessionData } = await supabase.auth.getSession()
      const token = sessionData.session?.access_token

      console.log('Making request to BFF:', `${BFF_BASE_URL}/documents/${doc.id}/process`)
      console.log('Token:', token ? 'Present' : 'Missing')

      const response = await fetch(
        `${BFF_BASE_URL}/documents/${doc.id}/process`,
        {
          method: 'POST',
          headers: {
            'X-Internal-Key': process.env.NEXT_PUBLIC_BFF_INTERNAL_KEY || '',
            ...(token ? { Authorization: `Bearer ${token}` } : {}),
          },
        }
      )

      if (!response.ok) {
        const errorData = await response.json()
        setError(errorData.error || 'Failed to start document processing')
        return
      }

      const result = await response.json()
      if (result.success && result.data?.task_id) {
        setSuccess(`Document processing started (Task ID: ${result.data.task_id})`)

        // Note: We don't update the document status to 'processing' here because
        // 'processing' is not a valid status in the database constraint.
        // The workers will update the status to 'extracted' -> 'suggested' as processing progresses.

        // Refresh documents list after a short delay to get the updated status from server
        setTimeout(() => {
          refreshDocuments()
        }, 1000)

        // Also refresh again after a longer delay to catch the worker update
        setTimeout(() => {
          refreshDocuments()
        }, 5000)
      } else {
        setError('Failed to start document processing')
      }
    } catch (err) {
      console.error('Error processing document:', err)
      setError('Failed to start document processing')
    } finally {
      setProcessingDocuments(prev => {
        const newSet = new Set(prev)
        newSet.delete(doc.id)
        return newSet
      })
    }
  }

  const handleDeleteDocument = async (doc: any) => {
    try {
      setError(null)

      if (!user) {
        setError('You must be signed in to delete documents')
        return
      }

      const filename = doc.path?.split('/').pop() || 'this document'
      if (!confirm(`Are you sure you want to delete "${filename}"? This action cannot be undone.`)) {
        return
      }

      setDeletingDocuments(prev => new Set(prev).add(doc.id))

      // Call BFF to delete document
      const { data: sessionData } = await supabase.auth.getSession()
      const token = sessionData.session?.access_token

      const response = await fetch(
        `${BFF_BASE_URL}/documents/${doc.id}`,
        {
          method: 'DELETE',
          headers: {
            'Content-Type': 'application/json',
            'X-Internal-Key': process.env.NEXT_PUBLIC_BFF_INTERNAL_KEY || '',
            ...(token ? { Authorization: `Bearer ${token}` } : {}),
          },
        }
      )

      if (!response.ok) {
        const errorData = await response.json()
        setError(errorData.error || 'Failed to delete document')
        return
      }

      const result = await response.json()
      if (result.success) {
        setSuccess('Document deleted successfully')
      } else {
        setError('Failed to delete document')
      }

      // Refresh documents list
      refreshDocuments()
    } catch (err) {
      console.error('Error deleting document:', err)
      setError('Failed to delete document')
    } finally {
      setDeletingDocuments(prev => {
        const newSet = new Set(prev)
        newSet.delete(doc.id)
        return newSet
      })
    }
  }

  const handleBatchProcess = async () => {
    try {
      setError(null)
      setSuccess(null)

      if (!user) {
        setError('You must be signed in to process documents')
        return
      }

      const unprocessedDocs = documents.filter(doc =>
        doc.status === 'uploaded' || doc.status === 'unprocessed'
      )

      if (unprocessedDocs.length === 0) {
        setError('No unprocessed documents found')
        return
      }

      if (!confirm(`Process ${unprocessedDocs.length} unprocessed documents?`)) {
        return
      }

      let successCount = 0
      let errorCount = 0

      for (const doc of unprocessedDocs) {
        try {
          await handleProcessDocument(doc)
          successCount++
        } catch (err) {
          console.error(`Failed to process document ${doc.id}:`, err)
          errorCount++
        }
      }

      if (errorCount === 0) {
        setSuccess(`Successfully started processing ${successCount} documents`)
      } else {
        setError(`Processed ${successCount} documents, ${errorCount} failed`)
      }
    } catch (err) {
      console.error('Error in batch processing:', err)
      setError('Failed to start batch processing')
    }
  }

  const handleUpload = async () => {
    try {
      setError(null)
      setSuccess(null)

      if (!user) {
        setError('You must be signed in to upload')
        return
      }
      if (!file) {
        setError('Please choose a file')
        return
      }
      // Resolve destination entity id robustly
      // SMEs: auto-pick current entity; if not yet resolved, fall back to single available entity
      // Firms: prefer explicit selection; otherwise use currently active entity
      let entityId: number | null = null
      if (isSME) {
        entityId =
          currentEntity?.entity_id ??
          (userContext?.entities?.length === 1
            ? userContext.entities[0].id
            : entities.length === 1
              ? entities[0].entity_id
              : null)
      } else {
        entityId = selectedEntityId
          ? Number(selectedEntityId)
          : (currentEntity?.entity_id ?? null)
      }

      // Final universal fallback: if there is exactly one entity visible, use it
      if (!entityId) {
        entityId = (userContext?.entities?.length === 1
          ? userContext.entities[0].id
          : entities.length === 1
            ? entities[0].entity_id
            : null)
      }

      if (!entityId) {
        setError(isFirm ? 'Please select a client file' : 'No entity available for upload')
        return
      }

      setUploading(true)

      const safeName = sanitizeName(file.name)
      const now = new Date()
      const yyyy = now.getFullYear()
      const mm = String(now.getMonth() + 1).padStart(2, '0')
      const objectKeyBase = `${yyyy}/${mm}/${Date.now()}-${safeName}`

      // 1) Upload file to Supabase Storage with bucket fallback
      const getObjectKeyForBucket = (bucket: string) => {
        // For 'inbox' bucket, do not prefix with 'inbox/' to avoid double nesting
        if (bucket === 'inbox') return `${entityId}/${objectKeyBase}`
        // For other buckets, keep files under an 'inbox/' prefix
        return `inbox/${entityId}/${objectKeyBase}`
      }

      const uploadToBucket = async (bucket: string) => {
        const key = getObjectKeyForBucket(bucket)
        const res = await supabase.storage
          .from(bucket)
          .upload(key, file, {
            cacheControl: '3600',
            upsert: false,
            contentType: file.type || 'application/octet-stream',
          })
        return { ...res, key }
      }

      let uploadData: { path: string } | null = null
      let uploadError: { message?: string } | null = null
      let bucketUsed = 'inbox'

      try {
        const preferred = process.env.NEXT_PUBLIC_STORAGE_BUCKET
        const candidates = [preferred, 'inbox', 'documents', 'invoices'].filter(Boolean) as string[]
        for (const bucket of candidates) {
          const res = await uploadToBucket(bucket)
          uploadData = res.data as { path: string } | null
          uploadError = (res as unknown as { error?: { message: string } }).error || null
          if (uploadData && !uploadError) {
            bucketUsed = bucket
            break
          }
        }
      } catch (e) {
        uploadError = { message: e instanceof Error ? e.message : 'Upload failed' }
      }

      if (uploadError || !uploadData) {
        setError(uploadError?.message || 'Upload failed')
        setUploading(false)
        return
      }

      // 2) Notify BFF to create document and start processing
      const { data: sessionData } = await supabase.auth.getSession()
      const token = sessionData.session?.access_token

      const resp = await fetch(
        `${BFF_BASE_URL}/entities/${entityId}/documents`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'X-Internal-Key': process.env.NEXT_PUBLIC_BFF_INTERNAL_KEY || '',
            ...(token ? { Authorization: `Bearer ${token}` } : {}),
          },
          body: JSON.stringify({
            entity_id: entityId,
            path: uploadData.path,
            mime_type: file.type || 'application/octet-stream',
            source: 'upload',
            // Optional: include bucketUsed in case server wants to use it later
            bucket: bucketUsed,
          }),
        }
      )

      if (!resp.ok) {
        const text = await resp.text()
        throw new Error(text || `BFF responded with ${resp.status}`)
      }

      setSuccess('Document uploaded. Processing has started.')
      setTimeout(() => {
        setShowUpload(false)
        // Refresh the documents list to show the new document
        refreshDocuments()
      }, 900)
    } catch (e) {
      setError(e instanceof Error ? e.message : 'Unexpected error')
    } finally {
      setUploading(false)
    }
  }
  return (
    <div style={getPageStyle(theme)}>
      <div style={{ maxWidth: '1200px', margin: '0 auto' }}>
        <div style={{ marginBottom: '32px' }}>
          <h1
            style={{
              fontSize: '32px',
              fontWeight: 600,
              color: theme.primaryText,
              margin: '0 0 8px 0',
            }}
          >
            Inbox
          </h1>
          <p
            style={{
              fontSize: '16px',
              color: theme.secondaryText,
              margin: 0,
            }}
          >
            Manage incoming documents and transactions
          </p>
        </div>

        {/* Filters and Actions */}
        <div
          style={{
            marginBottom: '24px',
            display: 'flex',
            flexDirection: 'column',
            gap: '16px',
          }}
        >
          <div
            style={{
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center',
              flexWrap: 'wrap',
              gap: '16px',
            }}
          >
            <div style={{ display: 'flex', gap: '12px', flexWrap: 'wrap' }}>
              <select style={getSelectStyle(theme)}>
                <option>All Items</option>
                <option>Unprocessed</option>
                <option>Processed</option>
                <option>Requires Review</option>
              </select>
              <select style={getSelectStyle(theme)}>
                <option>All Types</option>
                <option>Invoices</option>
                <option>Receipts</option>
                <option>Bank Statements</option>
              </select>
            </div>

            <div style={{ display: 'flex', gap: '12px' }}>
              <button
                onClick={onOpenUpload}
                style={getButtonStyle(theme, 'primary')}
                aria-label="Upload document"
              >
                Upload Document
              </button>
              <button
                style={getButtonStyle(theme, 'secondary')}
                onClick={handleBatchProcess}
                aria-label="Batch process all unprocessed documents"
              >
                Batch Process
              </button>
            </div>
          </div>
        </div>

        {/* Inbox List */}
        <div style={getCardStyle(theme)}>
          <div
            style={{
              padding: '24px',
              borderBottom: `1px solid ${theme.border}`,
            }}
          >
            <h2
              style={{
                fontSize: '20px',
                fontWeight: 600,
                color: theme.primaryText,
                margin: 0,
              }}
            >
              Document Queue
            </h2>
          </div>

          {/* Global notifications for document operations */}
          {(error || success) && (
            <div style={{
              padding: '12px 24px',
              borderBottom: `1px solid ${theme.border}`,
              backgroundColor: error ? '#fef2f2' : '#f0fdf4'
            }}>
              {error && (
                <div style={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: '8px',
                  color: theme.error,
                  fontSize: '14px'
                }}>
                  <span>⚠️</span>
                  <span>{error}</span>
                  <button
                    onClick={() => setError(null)}
                    style={{
                      marginLeft: 'auto',
                      background: 'none',
                      border: 'none',
                      color: theme.error,
                      cursor: 'pointer',
                      fontSize: '16px'
                    }}
                    aria-label="Dismiss error"
                  >
                    ×
                  </button>
                </div>
              )}
              {success && (
                <div style={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: '8px',
                  color: theme.success,
                  fontSize: '14px'
                }}>
                  <span>✅</span>
                  <span>{success}</span>
                  <button
                    onClick={() => setSuccess(null)}
                    style={{
                      marginLeft: 'auto',
                      background: 'none',
                      border: 'none',
                      color: theme.success,
                      cursor: 'pointer',
                      fontSize: '16px'
                    }}
                    aria-label="Dismiss success message"
                  >
                    ×
                  </button>
                </div>
              )}
            </div>
          )}

          <div>
            {/* Loading state */}
            {documentsLoading && (
              <div style={{ padding: '40px', textAlign: 'center' }}>
                <p style={{ color: theme.secondaryText, margin: 0 }}>
                  Loading documents...
                </p>
              </div>
            )}

            {/* Error state */}
            {documentsError && !documentsLoading && (
              <div style={{ padding: '40px', textAlign: 'center' }}>
                <p style={{ color: theme.error, margin: 0 }}>
                  Error loading documents: {documentsError}
                </p>
                <button
                  onClick={refreshDocuments}
                  style={{
                    marginTop: '16px',
                    padding: '8px 16px',
                    background: theme.primaryAction,
                    color: 'white',
                    border: 'none',
                    borderRadius: '6px',
                    cursor: 'pointer',
                  }}
                >
                  Retry
                </button>
              </div>
            )}

            {/* Empty state */}
            {!documentsLoading && !documentsError && documents.length === 0 && (
              <div style={{ padding: '40px', textAlign: 'center' }}>
                <p style={{ color: theme.secondaryText, margin: 0 }}>
                  No documents found. Upload your first document to get started.
                </p>
              </div>
            )}

            {/* Documents list */}
            {!documentsLoading && !documentsError && documents.length > 0 && documents.map(doc => {
              const statusDisplay = getStatusDisplay(doc.status)
              return (
              <div
                key={doc.id}
                style={{
                  padding: '20px 24px',
                  borderBottom: `1px solid ${theme.border}`,
                  transition: 'background-color 0.2s ease',
                  cursor: 'pointer',
                }}
                onMouseEnter={e => {
                  e.currentTarget.style.backgroundColor = theme.bg
                }}
                onMouseLeave={e => {
                  e.currentTarget.style.backgroundColor = 'transparent'
                }}
              >
                <div
                  style={{
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'space-between',
                  }}
                >
                  <div style={{ flex: 1, minWidth: 0 }}>
                    <div
                      style={{
                        display: 'flex',
                        alignItems: 'center',
                        gap: '12px',
                      }}
                    >
                      <div style={{ flexShrink: 0 }}>
                        <div
                          style={{
                            width: '40px',
                            height: '40px',
                            backgroundColor: theme.border,
                            borderRadius: '50%',
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center',
                          }}
                        >
                          <span style={{ fontSize: '16px' }}>
                            {doc.mime_type.includes('pdf')
                              ? '📄'
                              : doc.mime_type.includes('image')
                                ? '🖼️'
                                : '📄'}
                          </span>
                        </div>
                      </div>

                      <div style={{ flex: 1 }}>
                        <p
                          style={{
                            fontSize: '16px',
                            fontWeight: 500,
                            color: theme.primaryText,
                            margin: '0 0 4px 0',
                          }}
                        >
                          {getDocumentDescription(doc)}
                        </p>
                        <div
                          style={{
                            display: 'flex',
                            alignItems: 'center',
                            gap: '16px',
                          }}
                        >
                          <span
                            style={{
                              fontSize: '14px',
                              color: theme.secondaryText,
                            }}
                          >
                            {getDocumentType(doc.mime_type)}
                          </span>
                          <span
                            style={{
                              fontSize: '14px',
                              color: theme.secondaryText,
                            }}
                          >
                            {formatDate(doc.created_at)}
                          </span>
                          {doc.gross_amount && (
                            <span
                              style={{
                                fontSize: '14px',
                                fontWeight: 500,
                                color: theme.primaryText,
                              }}
                            >
                              {formatAmount(doc.gross_amount)}
                            </span>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>

                  <div
                    style={{
                      display: 'flex',
                      alignItems: 'center',
                      gap: '12px',
                    }}
                  >
                    <span
                      style={{
                        fontSize: '12px',
                        fontWeight: 500,
                        color: statusDisplay.color,
                        textTransform: 'uppercase',
                        letterSpacing: '0.5px',
                      }}
                    >
                      {statusDisplay.text}
                    </span>

                    <div style={{ display: 'flex', gap: '4px' }}>
                      <button
                        style={{
                          padding: '8px',
                          background: 'none',
                          border: 'none',
                          color: viewingDocuments.has(doc.id) ? theme.subtleText : theme.secondaryText,
                          cursor: viewingDocuments.has(doc.id) ? 'not-allowed' : 'pointer',
                          borderRadius: '6px',
                          transition: 'all 0.15s ease',
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center',
                          opacity: viewingDocuments.has(doc.id) ? 0.5 : 1,
                        }}
                        onMouseEnter={(e) => {
                          if (!viewingDocuments.has(doc.id)) {
                            e.currentTarget.style.color = theme.primaryText;
                            e.currentTarget.style.backgroundColor = theme.border;
                          }
                        }}
                        onMouseLeave={(e) => {
                          if (!viewingDocuments.has(doc.id)) {
                            e.currentTarget.style.color = theme.secondaryText;
                            e.currentTarget.style.backgroundColor = 'transparent';
                          }
                        }}
                        onClick={() => !viewingDocuments.has(doc.id) && handleViewDocument(doc)}
                        disabled={viewingDocuments.has(doc.id)}
                        aria-label={viewingDocuments.has(doc.id) ? "Loading document..." : "View document"}
                      >
                        {viewingDocuments.has(doc.id) ? '⏳' : ZenIcons.view()}
                      </button>
                      <button
                        style={{
                          padding: '8px',
                          background: 'none',
                          border: 'none',
                          color: processingDocuments.has(doc.id) ? theme.subtleText : theme.secondaryText,
                          cursor: processingDocuments.has(doc.id) ? 'not-allowed' : 'pointer',
                          borderRadius: '6px',
                          transition: 'all 0.15s ease',
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center',
                          opacity: processingDocuments.has(doc.id) ? 0.5 : 1,
                        }}
                        onMouseEnter={(e) => {
                          if (!processingDocuments.has(doc.id) && doc.status !== 'processing') {
                            e.currentTarget.style.color = theme.primaryText;
                            e.currentTarget.style.backgroundColor = theme.border;
                          }
                        }}
                        onMouseLeave={(e) => {
                          if (!processingDocuments.has(doc.id) && doc.status !== 'processing') {
                            e.currentTarget.style.color = theme.secondaryText;
                            e.currentTarget.style.backgroundColor = 'transparent';
                          }
                        }}
                        onClick={() => !processingDocuments.has(doc.id) && handleProcessDocument(doc)}
                        disabled={processingDocuments.has(doc.id)}
                        aria-label={
                          processingDocuments.has(doc.id)
                            ? "Processing..."
                            : canBeReviewed(doc.status)
                              ? "Review document"
                              : "Process document"
                        }
                        title={
                          canBeReviewed(doc.status)
                            ? "Review extracted data and confirm"
                            : canBeProcessed(doc.status)
                              ? "Start AI processing"
                              : "Document already processed"
                        }
                      >
                        {processingDocuments.has(doc.id)
                          ? '⏳'
                          : canBeReviewed(doc.status)
                            ? ZenIcons.review()
                            : ZenIcons.edit()
                        }
                      </button>
                      <button
                        style={{
                          padding: '8px',
                          background: 'none',
                          border: 'none',
                          color: deletingDocuments.has(doc.id) ? theme.subtleText : theme.secondaryText,
                          cursor: deletingDocuments.has(doc.id) ? 'not-allowed' : 'pointer',
                          borderRadius: '6px',
                          transition: 'all 0.15s ease',
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center',
                          opacity: deletingDocuments.has(doc.id) ? 0.5 : 1,
                        }}
                        onMouseEnter={(e) => {
                          if (!deletingDocuments.has(doc.id)) {
                            e.currentTarget.style.color = theme.error;
                            e.currentTarget.style.backgroundColor = theme.border;
                          }
                        }}
                        onMouseLeave={(e) => {
                          if (!deletingDocuments.has(doc.id)) {
                            e.currentTarget.style.color = theme.secondaryText;
                            e.currentTarget.style.backgroundColor = 'transparent';
                          }
                        }}
                        onClick={() => !deletingDocuments.has(doc.id) && handleDeleteDocument(doc)}
                        disabled={deletingDocuments.has(doc.id)}
                        aria-label={deletingDocuments.has(doc.id) ? "Deleting..." : "Delete document"}
                      >
                        {deletingDocuments.has(doc.id) ? '⏳' : ZenIcons.delete()}
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            )
            })}
          </div>

          {/* Footer */}
          <div
            style={{
              padding: '20px 24px',
              textAlign: 'center',
              borderTop: `1px solid ${theme.border}`,
            }}
          >
            <p
              style={{
                fontSize: '14px',
                color: theme.secondaryText,
                margin: 0,
              }}
            >
              {pagination ?
                `Showing ${documents.length} of ${pagination.total} items` :
                `${documents.length} items`
              }
            </p>
          </div>
        </div>

        {/* Stats Cards */}
        <div
          style={{
            marginTop: '32px',
            display: 'grid',
            gap: '24px',
            gridTemplateColumns: 'repeat(auto-fit, minmax(280px, 1fr))',
          }}
        >
          <div style={getCardStyle(theme)}>
            <div style={{ padding: '24px' }}>
              <div style={{ display: 'flex', alignItems: 'center' }}>
                <div style={{ flexShrink: 0 }}>
                  <div
                    style={{
                      width: '48px',
                      height: '48px',
                      backgroundColor: theme.surface,
                      border: `1px solid ${theme.border}`,
                      borderRadius: '50%',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                    }}
                  >
                    <span style={{ fontSize: '20px', opacity: 0.6 }}>⏳</span>
                  </div>
                </div>
                <div style={{ marginLeft: '16px' }}>
                  <p
                    style={{
                      fontSize: '12px', // caption style from guide
                      fontWeight: 500,
                      color: theme.secondaryText,
                      margin: '0 0 8px 0',
                      textTransform: 'uppercase',
                      letterSpacing: '0.5px',
                    }}
                  >
                    Pending
                  </p>
                  <p
                    style={{
                      fontSize: '32px', // display size for emphasis
                      fontWeight: 600,
                      color: theme.primaryText,
                      margin: 0,
                      lineHeight: 1.2,
                    }}
                  >
                    1
                  </p>
                </div>
              </div>
            </div>
          </div>

          <div style={getCardStyle(theme)}>
            <div style={{ padding: '24px' }}>
              <div style={{ display: 'flex', alignItems: 'center' }}>
                <div style={{ flexShrink: 0 }}>
                  <div
                    style={{
                      width: '48px',
                      height: '48px',
                      backgroundColor: theme.surface,
                      border: `1px solid ${theme.border}`,
                      borderRadius: '50%',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                    }}
                  >
                    <span style={{ fontSize: '20px', opacity: 0.6 }}>🔍</span>
                  </div>
                </div>
                <div style={{ marginLeft: '16px' }}>
                  <p
                    style={{
                      fontSize: '12px', // caption style from guide
                      fontWeight: 500,
                      color: theme.secondaryText,
                      margin: '0 0 8px 0',
                      textTransform: 'uppercase',
                      letterSpacing: '0.5px',
                    }}
                  >
                    Review Required
                  </p>
                  <p
                    style={{
                      fontSize: '32px', // display size for emphasis
                      fontWeight: 600,
                      color: theme.primaryText,
                      margin: 0,
                      lineHeight: 1.2,
                    }}
                  >
                    1
                  </p>
                </div>
              </div>
            </div>
          </div>

          <div style={getCardStyle(theme)}>
            <div style={{ padding: '24px' }}>
              <div style={{ display: 'flex', alignItems: 'center' }}>
                <div style={{ flexShrink: 0 }}>
                  <div
                    style={{
                      width: '48px',
                      height: '48px',
                      backgroundColor: theme.surface,
                      border: `1px solid ${theme.border}`,
                      borderRadius: '50%',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                    }}
                  >
                    <span style={{ fontSize: '20px', opacity: 0.6 }}>✅</span>
                  </div>
                </div>
                <div style={{ marginLeft: '16px' }}>
                  <p
                    style={{
                      fontSize: '12px', // caption style from guide
                      fontWeight: 500,
                      color: theme.secondaryText,
                      margin: '0 0 8px 0',
                      textTransform: 'uppercase',
                      letterSpacing: '0.5px',
                    }}
                  >
                    Processed
                  </p>
                  <p
                    style={{
                      fontSize: '32px', // display size for emphasis
                      fontWeight: 600,
                      color: theme.primaryText,
                      margin: 0,
                      lineHeight: 1.2,
                    }}
                  >
                    1
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Upload Modal */}
        {showUpload && (
          <div
            style={{
              position: 'fixed',
              inset: 0,
              zIndex: 50,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              backgroundColor: 'rgba(0, 0, 0, 0.4)',
            }}
          >
            <div
              style={{
                ...getCardStyle(theme),
                width: '100%',
                maxWidth: '480px',
                padding: '32px',
                margin: '16px',
              }}
            >
              <h3
                style={{
                  fontSize: '20px',
                  fontWeight: 600,
                  color: theme.primaryText,
                  margin: '0 0 8px 0',
                }}
              >
                Upload Document
              </h3>
              <p
                style={{
                  fontSize: '14px',
                  color: theme.secondaryText,
                  margin: '0 0 24px 0',
                }}
              >
                {isSME
                  ? 'PDFs and images are supported. Documents will be added to your company books.'
                  : 'PDFs and images are supported.'
                }
              </p>

              {/* Entity selection - only show for firms when they need to select/confirm entity */}
              {isFirm && (
                <div style={{ marginBottom: '20px' }}>
                  <label
                    style={{
                      display: 'block',
                      fontSize: '14px',
                      fontWeight: 500,
                      color: theme.primaryText,
                      marginBottom: '8px',
                    }}
                  >
                    Client File
                  </label>
                  <select
                    value={selectedEntityId ?? ''}
                    onChange={e => setSelectedEntityId(e.target.value || null)}
                    style={{
                      ...getSelectStyle(theme),
                      width: '100%',
                    }}
                  >
                    <option value="">Choose a client file…</option>
                    {entities.map(e => (
                      <option key={e.entity_id} value={String(e.entity_id)}>
                        {e.entity_name}
                      </option>
                    ))}
                  </select>
                  {selectedEntityId && (
                    <p style={{
                      fontSize: '12px',
                      color: theme.secondaryText,
                      margin: '4px 0 0 0',
                    }}>
                      Document will be uploaded to {entities.find(e => e.entity_id === Number(selectedEntityId))?.entity_name}
                    </p>
                  )}
                </div>
              )}

              {/* SME destination info */}
              {isSME && smeDestinationName && (
                <div style={{
                  marginBottom: '20px',
                  padding: '12px',
                  backgroundColor: '#f9fafb',
                  borderRadius: '8px',
                  border: '1px solid #e5e7eb'
                }}>
                  <p style={{
                    fontSize: '14px',
                    color: theme.secondaryText,
                    margin: 0,
                  }}>
                    <strong>Destination:</strong> {smeDestinationName}
                  </p>
                </div>
              )}

              <div style={{ marginBottom: '20px' }}>
                <label
                  style={{
                    display: 'block',
                    fontSize: '14px',
                    fontWeight: 500,
                    color: theme.primaryText,
                    marginBottom: '8px',
                  }}
                >
                  Choose File
                </label>
                <input
                  type="file"
                  accept="application/pdf,image/*"
                  onChange={e => setFile(e.target.files?.[0] || null)}
                  style={{
                    display: 'block',
                    width: '100%',
                    fontSize: '14px',
                    color: theme.primaryText,
                    padding: '8px 12px',
                    border: `1px solid ${theme.border}`,
                    borderRadius: '8px',
                    backgroundColor: theme.surface,
                  }}
                />
              </div>

              {error && (
                <p
                  style={{
                    fontSize: '14px',
                    color: '#DC2626',
                    margin: '0 0 16px 0',
                  }}
                >
                  {error}
                </p>
              )}
              {success && (
                <p
                  style={{
                    fontSize: '14px',
                    color: '#059669',
                    margin: '0 0 16px 0',
                  }}
                >
                  {success}
                </p>
              )}

              <div
                style={{
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'flex-end',
                  gap: '12px',
                  marginTop: '24px',
                }}
              >
                <button
                  onClick={() => setShowUpload(false)}
                  style={{
                    ...getButtonStyle(theme, 'secondary'),
                    opacity: uploading ? 0.6 : 1,
                    cursor: uploading ? 'not-allowed' : 'pointer',
                  }}
                  disabled={uploading}
                >
                  Cancel
                </button>
                <button
                  onClick={() => {
                    void handleUpload()
                  }}
                  style={{
                    ...getButtonStyle(theme, 'primary'),
                    opacity: uploading ? 0.6 : 1,
                    cursor: uploading ? 'not-allowed' : 'pointer',
                  }}
                  disabled={uploading}
                >
                  {uploading ? 'Uploading…' : 'Upload'}
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}
