'use client'

import React, { useState, useEffect, useCallback } from 'react'
import { useRouter, useParams } from 'next/navigation'
import { createSecureBrowserClient } from '@/lib/session-security'
import { useOrgEntitySelection } from '@/hooks/useOrgEntitySelection'
import { useAuth } from '@/contexts/AuthContext'
import { ZenIcons } from '@/components/ZenIcons'
import type {
  InboxDocument,
  DocumentReviewFormData,
  DocumentReviewFormErrors,
  ExtractionResult
} from '@/types/document-review'
import ChangeConfirmationModal from '@/components/ChangeConfirmationModal'

const BFF_BASE_URL = process.env.NEXT_PUBLIC_BFF_URL || 'http://localhost:4000'

// Zen UI Theme
const zenTheme = {
  bg: '#FBFAF5',
  surface: '#FFFFFC',
  primaryText: '#1a1a1a',
  secondaryText: '#6b7280',
  subtleText: '#9ca3af',
  border: '#f3f4f6',
  borderHover: '#e5e7eb',
  success: '#10b981',
  warning: '#f59e0b',
  error: '#ef4444',
  primaryAction: '#3b82f6',
  shadow: '0 1px 3px 0 rgba(0, 0, 0, 0.05)',
  shadowHover: '0 1px 3px 0 rgba(0, 0, 0, 0.1)',
}

export default function DocumentReviewPage() {
  const router = useRouter()
  const params = useParams()
  const documentId = parseInt(params.documentId as string)
  const supabase = createSecureBrowserClient()
  const { user } = useAuth()
  const { currentEntity } = useOrgEntitySelection()

  // State
  const [document, setDocument] = useState<InboxDocument | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [documentUrl, setDocumentUrl] = useState<string | null>(null)
  const [submitting, setSubmitting] = useState(false)
  const [success, setSuccess] = useState<string | null>(null)

  // Navigation state
  const [reviewableDocuments, setReviewableDocuments] = useState<number[]>([])
  const [currentIndex, setCurrentIndex] = useState(-1)

  // Form state
  const [formData, setFormData] = useState<DocumentReviewFormData>({
    supplierName: '',
    supplierVat: '',
    supplierIban: '',
    supplierAddress: '',
    invoiceNumber: '',
    invoiceIssueDate: '',
    invoiceDueDate: '',
    invoiceCurrency: 'EUR',
    invoiceNet: '',
    invoiceVat: '',
    invoiceGross: '',
    journalDate: '',
    journalReference: '',
    journalDescription: '',
    lines: [],
    suggestionLines: [],
  })

  const [formErrors, setFormErrors] = useState<DocumentReviewFormErrors>({})
  const [hasChanges, setHasChanges] = useState(false)
  const [originalFormData, setOriginalFormData] = useState<DocumentReviewFormData | null>(null)

  // Change confirmation modal state
  const [showChangeConfirmation, setShowChangeConfirmation] = useState(false)
  const [pendingChanges, setPendingChanges] = useState<Array<{
    field: string
    label: string
    oldValue: string
    newValue: string
  }>>([])
  const [confirmingChanges, setConfirmingChanges] = useState(false)

  // Fetch reviewable documents for navigation
  const fetchReviewableDocuments = useCallback(async () => {
    if (!user || !currentEntity) return

    try {
      const { data: sessionData } = await supabase.auth.getSession()
      const token = sessionData.session?.access_token

      // Fetch documents with extracted or suggested status
      const { data, error } = await supabase
        .from('inbox_documents')
        .select('id')
        .eq('entity_id', currentEntity.entity_id!)
        .in('status', ['extracted', 'suggested'])
        .order('created_at', { ascending: false })

      if (error) {
        console.error('Error fetching reviewable documents:', error)
        return
      }

      const documentIds = (data || []).map(doc => doc.id)
      setReviewableDocuments(documentIds)

      // Find current document index
      const index = documentIds.indexOf(documentId)
      setCurrentIndex(index)

    } catch (err) {
      console.error('Error fetching reviewable documents:', err)
    }
  }, [user, currentEntity, documentId, supabase])

  // Fetch document data
  const fetchDocument = useCallback(async () => {
    if (!documentId || !user) return

    try {
      setLoading(true)
      setError(null)

      const { data: sessionData } = await supabase.auth.getSession()
      const token = sessionData.session?.access_token

      // Fetch document details from BFF
      const response = await fetch(`${BFF_BASE_URL}/documents/${documentId}`, {
        headers: {
          'Content-Type': 'application/json',
          'X-Internal-Key': process.env.NEXT_PUBLIC_BFF_INTERNAL_KEY || '',
          ...(token ? { Authorization: `Bearer ${token}` } : {}),
        },
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to fetch document')
      }

      const result = await response.json()
      const docData = result.data as InboxDocument

      setDocument(docData)
      initializeFormData(docData)

      // Fetch document URL for viewing
      const urlResponse = await fetch(`${BFF_BASE_URL}/documents/${documentId}/download`, {
        headers: {
          'Content-Type': 'application/json',
          'X-Internal-Key': process.env.NEXT_PUBLIC_BFF_INTERNAL_KEY || '',
          ...(token ? { Authorization: `Bearer ${token}` } : {}),
        },
      })

      if (urlResponse.ok) {
        const urlResult = await urlResponse.json()
        if (urlResult.success && urlResult.data?.download_url) {
          setDocumentUrl(urlResult.data.download_url)
        }
      }

    } catch (err) {
      console.error('Error fetching document:', err)
      setError(err instanceof Error ? err.message : 'Failed to fetch document')
    } finally {
      setLoading(false)
    }
  }, [documentId, user, supabase])

  // Initialize form data from document
  const initializeFormData = (doc: InboxDocument) => {
    const extraction = doc.extraction
    const suggestion = doc.suggestion

    const initialData = {
      // Supplier information from extraction
      supplierName: extraction?.supplier?.name || doc.supplier_name || '',
      supplierVat: extraction?.supplier?.vat || '',
      supplierIban: extraction?.supplier?.iban || '',
      supplierAddress: extraction?.supplier?.address || '',

      // Invoice information from extraction
      invoiceNumber: extraction?.invoice?.number || doc.invoice_number || '',
      invoiceIssueDate: extraction?.invoice?.issueDate || doc.invoice_date || '',
      invoiceDueDate: extraction?.invoice?.dueDate || '',
      invoiceCurrency: 'EUR',
      invoiceNet: extraction?.invoice?.net || doc.net_amount || '',
      invoiceVat: extraction?.invoice?.vat || doc.vat_amount || '',
      invoiceGross: extraction?.invoice?.gross || doc.gross_amount || '',

      // Journal information from suggestion
      journalDate: suggestion?.journalDate || extraction?.invoice?.issueDate || '',
      journalReference: suggestion?.reference || extraction?.invoice?.number || '',
      journalDescription: suggestion?.description || `Invoice from ${extraction?.supplier?.name || 'Supplier'}`,

      // Line items from extraction
      lines: extraction?.lines?.map(line => ({
        description: line.description,
        quantity: line.quantity,
        unitPrice: line.unit_price,
        vatRate: line.vat_rate,
        accountHint: line.account_hint,
      })) || [],

      // Suggestion lines for review
      suggestionLines: suggestion?.lines?.map(line => ({
        accountId: line.accountId,
        debit: line.debit,
        credit: line.credit,
        vatCodeId: line.vatCodeId,
        memo: line.memo,
      })) || [],
    }

    setFormData(initialData)
    setOriginalFormData(initialData)
    setFormErrors({})
    setHasChanges(false)
  }

  useEffect(() => {
    fetchDocument()
    fetchReviewableDocuments()
  }, [fetchDocument, fetchReviewableDocuments])

  // Detect changes between original and current form data
  const detectChanges = () => {
    if (!originalFormData) return []

    const changes: Array<{
      field: string
      label: string
      oldValue: string
      newValue: string
    }> = []

    const fieldLabels: Record<keyof DocumentReviewFormData, string> = {
      supplierName: 'Supplier Name',
      supplierVat: 'VAT Number',
      supplierIban: 'IBAN',
      supplierAddress: 'Address',
      invoiceNumber: 'Invoice Number',
      invoiceIssueDate: 'Invoice Date',
      invoiceDueDate: 'Due Date',
      invoiceCurrency: 'Currency',
      invoiceNet: 'Net Amount',
      invoiceVat: 'VAT Amount',
      invoiceGross: 'Gross Amount',
      journalDate: 'Journal Date',
      journalReference: 'Journal Reference',
      journalDescription: 'Journal Description',
      lines: 'Line Items',
      suggestionLines: 'Suggestion Lines',
    }

    // Check each field for changes
    Object.keys(fieldLabels).forEach(key => {
      const field = key as keyof DocumentReviewFormData
      if (field === 'lines' || field === 'suggestionLines') return // Skip complex fields for now

      const oldValue = String(originalFormData[field] || '')
      const newValue = String(formData[field] || '')

      if (oldValue !== newValue) {
        changes.push({
          field,
          label: fieldLabels[field],
          oldValue,
          newValue,
        })
      }
    })

    return changes
  }

  // Handle form field changes
  const handleFieldChange = (field: keyof DocumentReviewFormData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }))
    setHasChanges(true)

    // Clear field-specific errors
    if (formErrors[field]) {
      setFormErrors(prev => ({ ...prev, [field]: undefined }))
    }
  }

  // Navigation functions
  const handleBack = () => {
    router.push('/inbox')
  }

  const handleNext = () => {
    if (currentIndex >= 0 && currentIndex < reviewableDocuments.length - 1) {
      const nextDocumentId = reviewableDocuments[currentIndex + 1]
      router.push(`/review/${nextDocumentId}`)
    }
  }

  const handlePrevious = () => {
    if (currentIndex > 0) {
      const previousDocumentId = reviewableDocuments[currentIndex - 1]
      router.push(`/review/${previousDocumentId}`)
    }
  }

  // Keyboard navigation
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.ctrlKey || event.metaKey) {
        switch (event.key) {
          case 'ArrowLeft':
            event.preventDefault()
            handlePrevious()
            break
          case 'ArrowRight':
            event.preventDefault()
            handleNext()
            break
          case 'Enter':
            event.preventDefault()
            if (!submitting) {
              handleConfirm()
            }
            break
        }
      }
    }

    window.addEventListener('keydown', handleKeyDown)
    return () => window.removeEventListener('keydown', handleKeyDown)
  }, [currentIndex, reviewableDocuments, submitting])

  // Show change confirmation if there are changes
  const handleConfirm = async () => {
    if (!document) return

    // Detect changes
    const changes = detectChanges()

    if (changes.length > 0) {
      // Show change confirmation modal
      setPendingChanges(changes)
      setShowChangeConfirmation(true)
      return
    }

    // No changes, proceed directly
    await confirmDocument()
  }

  // Actually confirm the document
  const confirmDocument = async () => {
    if (!document) return

    try {
      setSubmitting(true)
      setConfirmingChanges(true)
      setError(null)

      // Build corrections object if there are changes
      let corrections: Partial<ExtractionResult> | undefined
      if (hasChanges) {
        corrections = {
          supplier: {
            name: formData.supplierName,
            vat: formData.supplierVat,
            iban: formData.supplierIban,
            address: formData.supplierAddress,
          },
          invoice: {
            number: formData.invoiceNumber,
            issueDate: formData.invoiceIssueDate,
            dueDate: formData.invoiceDueDate,
            currency: 'EUR',
            net: formData.invoiceNet,
            vat: formData.invoiceVat,
            gross: formData.invoiceGross,
          },
          lines: formData.lines.map(line => ({
            description: line.description,
            quantity: line.quantity,
            unit_price: line.unitPrice,
            vat_rate: line.vatRate,
            account_hint: line.accountHint,
          })),
        }
      }

      const { data: sessionData } = await supabase.auth.getSession()
      const token = sessionData.session?.access_token

      const requestBody: any = { document_id: documentId }
      if (corrections) {
        requestBody.correction = corrections
      }

      const response = await fetch(`${BFF_BASE_URL}/documents/${documentId}/confirm`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-Internal-Key': process.env.NEXT_PUBLIC_BFF_INTERNAL_KEY || '',
          ...(token ? { Authorization: `Bearer ${token}` } : {}),
        },
        body: JSON.stringify(requestBody),
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to confirm document')
      }

      const result = await response.json()
      setSuccess(`Document confirmed successfully! Status: ${result.data?.status || 'processed'}`)
      
      // Navigate back to inbox after a short delay
      setTimeout(() => {
        router.push('/inbox')
      }, 2000)

    } catch (err) {
      console.error('Error confirming document:', err)
      setError(err instanceof Error ? err.message : 'Failed to confirm document')
    } finally {
      setSubmitting(false)
      setConfirmingChanges(false)
      setShowChangeConfirmation(false)
    }
  }

  // Handle change confirmation modal actions
  const handleConfirmChanges = () => {
    confirmDocument()
  }

  const handleCancelChanges = () => {
    setShowChangeConfirmation(false)
    setPendingChanges([])
  }

  if (loading) {
    return (
      <div style={{ 
        display: 'flex', 
        justifyContent: 'center', 
        alignItems: 'center', 
        height: '100vh',
        backgroundColor: zenTheme.bg 
      }}>
        <div style={{ color: zenTheme.secondaryText }}>Loading document...</div>
      </div>
    )
  }

  if (error && !document) {
    return (
      <div style={{ 
        display: 'flex', 
        flexDirection: 'column',
        justifyContent: 'center', 
        alignItems: 'center', 
        height: '100vh',
        backgroundColor: zenTheme.bg,
        gap: '16px'
      }}>
        <div style={{ color: zenTheme.error }}>Error: {error}</div>
        <button
          onClick={handleBack}
          style={{
            padding: '8px 16px',
            backgroundColor: zenTheme.primaryAction,
            color: 'white',
            border: 'none',
            borderRadius: '6px',
            cursor: 'pointer',
          }}
        >
          Back to Inbox
        </button>
      </div>
    )
  }

  return (
    <div style={{ 
      height: '100vh', 
      backgroundColor: zenTheme.bg,
      display: 'flex',
      flexDirection: 'column'
    }}>
      {/* Header */}
      <div style={{
        backgroundColor: zenTheme.surface,
        borderBottom: `1px solid ${zenTheme.border}`,
        padding: '16px 24px',
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        boxShadow: zenTheme.shadow,
      }}>
        <div style={{ display: 'flex', alignItems: 'center', gap: '16px' }}>
          <button
            onClick={handleBack}
            style={{
              padding: '8px',
              backgroundColor: 'transparent',
              border: 'none',
              color: zenTheme.secondaryText,
              cursor: 'pointer',
              borderRadius: '6px',
              display: 'flex',
              alignItems: 'center',
            }}
          >
            ← Back to Inbox
          </button>
          <div>
            <h1 style={{ 
              margin: 0, 
              fontSize: '20px', 
              fontWeight: 600, 
              color: zenTheme.primaryText 
            }}>
              Review Document
            </h1>
            <p style={{
              margin: 0,
              fontSize: '14px',
              color: zenTheme.secondaryText
            }}>
              {formData.supplierName || document?.supplier_name || 'Unknown Supplier'} - {formData.invoiceNumber || document?.invoice_number || 'No Invoice Number'}
            </p>
          </div>
        </div>

        <div style={{ display: 'flex', gap: '12px', alignItems: 'center' }}>
          {/* Document counter */}
          {reviewableDocuments.length > 0 && currentIndex >= 0 && (
            <div style={{
              fontSize: '14px',
              color: zenTheme.secondaryText,
              padding: '8px 12px',
            }}>
              {currentIndex + 1} of {reviewableDocuments.length}
            </div>
          )}

          {/* Navigation buttons */}
          <button
            onClick={handlePrevious}
            disabled={currentIndex <= 0}
            title="Previous document (Ctrl+←)"
            style={{
              padding: '8px 12px',
              backgroundColor: 'transparent',
              border: `1px solid ${zenTheme.border}`,
              color: currentIndex <= 0 ? zenTheme.subtleText : zenTheme.secondaryText,
              cursor: currentIndex <= 0 ? 'not-allowed' : 'pointer',
              borderRadius: '6px',
              display: 'flex',
              alignItems: 'center',
              gap: '4px',
              opacity: currentIndex <= 0 ? 0.5 : 1,
            }}
          >
            ← Previous
          </button>

          <button
            onClick={handleNext}
            disabled={currentIndex >= reviewableDocuments.length - 1}
            title="Next document (Ctrl+→)"
            style={{
              padding: '8px 12px',
              backgroundColor: 'transparent',
              border: `1px solid ${zenTheme.border}`,
              color: currentIndex >= reviewableDocuments.length - 1 ? zenTheme.subtleText : zenTheme.secondaryText,
              cursor: currentIndex >= reviewableDocuments.length - 1 ? 'not-allowed' : 'pointer',
              borderRadius: '6px',
              display: 'flex',
              alignItems: 'center',
              gap: '4px',
              opacity: currentIndex >= reviewableDocuments.length - 1 ? 0.5 : 1,
            }}
          >
            Next →
          </button>

          {/* Confirm button */}
          <button
            onClick={handleConfirm}
            disabled={submitting}
            title="Confirm document (Ctrl+Enter)"
            style={{
              padding: '8px 16px',
              backgroundColor: submitting ? zenTheme.subtleText : zenTheme.primaryAction,
              color: 'white',
              border: 'none',
              borderRadius: '6px',
              cursor: submitting ? 'not-allowed' : 'pointer',
              display: 'flex',
              alignItems: 'center',
              gap: '4px',
            }}
          >
            {submitting ? '⏳' : ZenIcons.review({ size: 14 })}
            {submitting ? 'Confirming...' : 'Confirm'}
          </button>
        </div>
      </div>

      {/* Success/Error Messages */}
      {(success || error) && (
        <div style={{
          padding: '12px 24px',
          backgroundColor: success ? '#f0fdf4' : '#fef2f2',
          borderBottom: `1px solid ${success ? zenTheme.success : zenTheme.error}`,
          color: success ? zenTheme.success : zenTheme.error,
          fontSize: '14px',
        }}>
          {success || error}
        </div>
      )}

      {/* Main Content - Split Screen */}
      <div style={{ 
        flex: 1, 
        display: 'flex',
        overflow: 'hidden'
      }}>
        {/* Left Panel - Form Fields */}
        <div style={{
          width: '50%',
          backgroundColor: zenTheme.surface,
          borderRight: `1px solid ${zenTheme.border}`,
          overflow: 'auto',
          padding: '24px',
        }}>
          <div style={{ maxWidth: '500px' }}>
            {/* Supplier Information */}
            <div style={{ marginBottom: '32px' }}>
              <h3 style={{
                fontSize: '18px',
                fontWeight: 600,
                color: zenTheme.primaryText,
                marginBottom: '16px',
                margin: '0 0 16px 0'
              }}>
                Supplier Information
              </h3>

              <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '16px', marginBottom: '16px' }}>
                <div>
                  <label style={{
                    display: 'block',
                    fontSize: '14px',
                    fontWeight: 500,
                    color: zenTheme.primaryText,
                    marginBottom: '6px'
                  }}>
                    Supplier Name *
                  </label>
                  <input
                    type="text"
                    value={formData.supplierName}
                    onChange={(e) => handleFieldChange('supplierName', e.target.value)}
                    placeholder="Enter supplier name"
                    style={{
                      width: '100%',
                      padding: '8px 12px',
                      border: `1px solid ${formErrors.supplierName ? zenTheme.error : zenTheme.border}`,
                      borderRadius: '6px',
                      fontSize: '14px',
                      backgroundColor: zenTheme.surface,
                      color: zenTheme.primaryText,
                      outline: 'none',
                      transition: 'border-color 0.15s ease',
                    }}
                    onFocus={(e) => e.target.style.borderColor = zenTheme.primaryAction}
                    onBlur={(e) => e.target.style.borderColor = formErrors.supplierName ? zenTheme.error : zenTheme.border}
                  />
                  {formErrors.supplierName && (
                    <div style={{ color: zenTheme.error, fontSize: '12px', marginTop: '4px' }}>
                      {formErrors.supplierName}
                    </div>
                  )}
                </div>

                <div>
                  <label style={{
                    display: 'block',
                    fontSize: '14px',
                    fontWeight: 500,
                    color: zenTheme.primaryText,
                    marginBottom: '6px'
                  }}>
                    VAT Number
                  </label>
                  <input
                    type="text"
                    value={formData.supplierVat}
                    onChange={(e) => handleFieldChange('supplierVat', e.target.value)}
                    placeholder="BE0123456789"
                    style={{
                      width: '100%',
                      padding: '8px 12px',
                      border: `1px solid ${zenTheme.border}`,
                      borderRadius: '6px',
                      fontSize: '14px',
                      backgroundColor: zenTheme.surface,
                      color: zenTheme.primaryText,
                      outline: 'none',
                      transition: 'border-color 0.15s ease',
                    }}
                    onFocus={(e) => e.target.style.borderColor = zenTheme.primaryAction}
                    onBlur={(e) => e.target.style.borderColor = zenTheme.border}
                  />
                </div>
              </div>

              <div>
                <label style={{
                  display: 'block',
                  fontSize: '14px',
                  fontWeight: 500,
                  color: zenTheme.primaryText,
                  marginBottom: '6px'
                }}>
                  Address
                </label>
                <textarea
                  value={formData.supplierAddress}
                  onChange={(e) => handleFieldChange('supplierAddress', e.target.value)}
                  placeholder="Enter supplier address"
                  rows={3}
                  style={{
                    width: '100%',
                    padding: '8px 12px',
                    border: `1px solid ${zenTheme.border}`,
                    borderRadius: '6px',
                    fontSize: '14px',
                    backgroundColor: zenTheme.surface,
                    color: zenTheme.primaryText,
                    outline: 'none',
                    transition: 'border-color 0.15s ease',
                    resize: 'vertical',
                    fontFamily: 'inherit',
                  }}
                  onFocus={(e) => e.target.style.borderColor = zenTheme.primaryAction}
                  onBlur={(e) => e.target.style.borderColor = zenTheme.border}
                />
              </div>
            </div>

            {/* Invoice Information */}
            <div style={{ marginBottom: '32px' }}>
              <h3 style={{
                fontSize: '18px',
                fontWeight: 600,
                color: zenTheme.primaryText,
                marginBottom: '16px',
                margin: '0 0 16px 0'
              }}>
                Invoice Information
              </h3>

              <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '16px', marginBottom: '16px' }}>
                <div>
                  <label style={{
                    display: 'block',
                    fontSize: '14px',
                    fontWeight: 500,
                    color: zenTheme.primaryText,
                    marginBottom: '6px'
                  }}>
                    Invoice Number *
                  </label>
                  <input
                    type="text"
                    value={formData.invoiceNumber}
                    onChange={(e) => handleFieldChange('invoiceNumber', e.target.value)}
                    placeholder="Enter invoice number"
                    style={{
                      width: '100%',
                      padding: '8px 12px',
                      border: `1px solid ${formErrors.invoiceNumber ? zenTheme.error : zenTheme.border}`,
                      borderRadius: '6px',
                      fontSize: '14px',
                      backgroundColor: zenTheme.surface,
                      color: zenTheme.primaryText,
                      outline: 'none',
                      transition: 'border-color 0.15s ease',
                    }}
                    onFocus={(e) => e.target.style.borderColor = zenTheme.primaryAction}
                    onBlur={(e) => e.target.style.borderColor = formErrors.invoiceNumber ? zenTheme.error : zenTheme.border}
                  />
                  {formErrors.invoiceNumber && (
                    <div style={{ color: zenTheme.error, fontSize: '12px', marginTop: '4px' }}>
                      {formErrors.invoiceNumber}
                    </div>
                  )}
                </div>

                <div>
                  <label style={{
                    display: 'block',
                    fontSize: '14px',
                    fontWeight: 500,
                    color: zenTheme.primaryText,
                    marginBottom: '6px'
                  }}>
                    Invoice Date *
                  </label>
                  <input
                    type="date"
                    value={formData.invoiceIssueDate}
                    onChange={(e) => handleFieldChange('invoiceIssueDate', e.target.value)}
                    style={{
                      width: '100%',
                      padding: '8px 12px',
                      border: `1px solid ${formErrors.invoiceIssueDate ? zenTheme.error : zenTheme.border}`,
                      borderRadius: '6px',
                      fontSize: '14px',
                      backgroundColor: zenTheme.surface,
                      color: zenTheme.primaryText,
                      outline: 'none',
                      transition: 'border-color 0.15s ease',
                    }}
                    onFocus={(e) => e.target.style.borderColor = zenTheme.primaryAction}
                    onBlur={(e) => e.target.style.borderColor = formErrors.invoiceIssueDate ? zenTheme.error : zenTheme.border}
                  />
                  {formErrors.invoiceIssueDate && (
                    <div style={{ color: zenTheme.error, fontSize: '12px', marginTop: '4px' }}>
                      {formErrors.invoiceIssueDate}
                    </div>
                  )}
                </div>
              </div>

              <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr 1fr', gap: '16px' }}>
                <div>
                  <label style={{
                    display: 'block',
                    fontSize: '14px',
                    fontWeight: 500,
                    color: zenTheme.primaryText,
                    marginBottom: '6px'
                  }}>
                    Net Amount
                  </label>
                  <input
                    type="text"
                    value={formData.invoiceNet}
                    onChange={(e) => handleFieldChange('invoiceNet', e.target.value)}
                    placeholder="0.00"
                    style={{
                      width: '100%',
                      padding: '8px 12px',
                      border: `1px solid ${zenTheme.border}`,
                      borderRadius: '6px',
                      fontSize: '14px',
                      backgroundColor: zenTheme.surface,
                      color: zenTheme.primaryText,
                      outline: 'none',
                      transition: 'border-color 0.15s ease',
                    }}
                    onFocus={(e) => e.target.style.borderColor = zenTheme.primaryAction}
                    onBlur={(e) => e.target.style.borderColor = zenTheme.border}
                  />
                </div>

                <div>
                  <label style={{
                    display: 'block',
                    fontSize: '14px',
                    fontWeight: 500,
                    color: zenTheme.primaryText,
                    marginBottom: '6px'
                  }}>
                    VAT Amount
                  </label>
                  <input
                    type="text"
                    value={formData.invoiceVat}
                    onChange={(e) => handleFieldChange('invoiceVat', e.target.value)}
                    placeholder="0.00"
                    style={{
                      width: '100%',
                      padding: '8px 12px',
                      border: `1px solid ${zenTheme.border}`,
                      borderRadius: '6px',
                      fontSize: '14px',
                      backgroundColor: zenTheme.surface,
                      color: zenTheme.primaryText,
                      outline: 'none',
                      transition: 'border-color 0.15s ease',
                    }}
                    onFocus={(e) => e.target.style.borderColor = zenTheme.primaryAction}
                    onBlur={(e) => e.target.style.borderColor = zenTheme.border}
                  />
                </div>

                <div>
                  <label style={{
                    display: 'block',
                    fontSize: '14px',
                    fontWeight: 500,
                    color: zenTheme.primaryText,
                    marginBottom: '6px'
                  }}>
                    Gross Amount
                  </label>
                  <input
                    type="text"
                    value={formData.invoiceGross}
                    onChange={(e) => handleFieldChange('invoiceGross', e.target.value)}
                    placeholder="0.00"
                    style={{
                      width: '100%',
                      padding: '8px 12px',
                      border: `1px solid ${zenTheme.border}`,
                      borderRadius: '6px',
                      fontSize: '14px',
                      backgroundColor: zenTheme.surface,
                      color: zenTheme.primaryText,
                      outline: 'none',
                      transition: 'border-color 0.15s ease',
                    }}
                    onFocus={(e) => e.target.style.borderColor = zenTheme.primaryAction}
                    onBlur={(e) => e.target.style.borderColor = zenTheme.border}
                  />
                </div>
              </div>
            </div>

            {/* Suggested Journal Lines */}
            {formData.suggestionLines.length > 0 && (
              <div style={{ marginBottom: '32px' }}>
                <h3 style={{
                  fontSize: '18px',
                  fontWeight: 600,
                  color: zenTheme.primaryText,
                  marginBottom: '16px',
                  margin: '0 0 16px 0'
                }}>
                  Suggested Journal Lines
                </h3>

                <div style={{
                  border: `1px solid ${zenTheme.border}`,
                  borderRadius: '6px',
                  overflow: 'hidden',
                }}>
                  <div style={{
                    backgroundColor: zenTheme.bg,
                    padding: '8px 12px',
                    fontSize: '12px',
                    fontWeight: 500,
                    color: zenTheme.secondaryText,
                    display: 'grid',
                    gridTemplateColumns: '1fr 80px 80px',
                    gap: '8px',
                  }}>
                    <div>Account</div>
                    <div>Debit</div>
                    <div>Credit</div>
                  </div>

                  {formData.suggestionLines.map((line, index) => (
                    <div
                      key={index}
                      style={{
                        padding: '8px 12px',
                        fontSize: '14px',
                        color: zenTheme.primaryText,
                        display: 'grid',
                        gridTemplateColumns: '1fr 80px 80px',
                        gap: '8px',
                        borderTop: index > 0 ? `1px solid ${zenTheme.border}` : 'none',
                      }}
                    >
                      <div>{line.accountId} {line.memo && `- ${line.memo}`}</div>
                      <div>€{line.debit}</div>
                      <div>€{line.credit}</div>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Right Panel - Document Viewer */}
        <div style={{
          width: '50%',
          backgroundColor: zenTheme.bg,
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center',
          padding: '24px',
        }}>
          {documentUrl ? (
            <div style={{
              width: '100%',
              height: '100%',
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'center',
              justifyContent: 'center',
              border: `2px dashed ${zenTheme.border}`,
              borderRadius: '12px',
              backgroundColor: zenTheme.surface,
              textAlign: 'center',
              gap: '16px',
            }}>
              <div style={{
                fontSize: '48px',
                color: zenTheme.subtleText,
              }}>
                📄
              </div>
              <div>
                <h3 style={{
                  margin: '0 0 8px 0',
                  fontSize: '18px',
                  color: zenTheme.primaryText,
                }}>
                  Document Ready for Review
                </h3>
                <p style={{
                  margin: '0 0 16px 0',
                  fontSize: '14px',
                  color: zenTheme.secondaryText,
                }}>
                  Click below to open the document in a new tab for reference
                </p>
                <button
                  onClick={() => window.open(documentUrl, '_blank')}
                  style={{
                    padding: '12px 24px',
                    backgroundColor: zenTheme.primaryAction,
                    color: 'white',
                    border: 'none',
                    borderRadius: '8px',
                    fontSize: '14px',
                    fontWeight: 500,
                    cursor: 'pointer',
                    display: 'flex',
                    alignItems: 'center',
                    gap: '8px',
                    margin: '0 auto',
                  }}
                >
                  {ZenIcons.view({ size: 16 })}
                  Open Document
                </button>
              </div>
            </div>
          ) : (
            <div style={{
              color: zenTheme.secondaryText,
              textAlign: 'center',
            }}>
              <div style={{ marginBottom: '8px' }}>📄</div>
              <div>Document preview not available</div>
            </div>
          )}
        </div>
      </div>

      {/* Change Confirmation Modal */}
      <ChangeConfirmationModal
        isOpen={showChangeConfirmation}
        changes={pendingChanges}
        onConfirm={handleConfirmChanges}
        onCancel={handleCancelChanges}
        loading={confirmingChanges}
      />
    </div>
  )
}
