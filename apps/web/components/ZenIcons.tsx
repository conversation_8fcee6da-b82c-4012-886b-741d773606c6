import React from 'react'

// Zen UI Icons - Minimal, geometric, calming alternatives to traditional icons
// Following zen design principles: simplicity, clarity, and emotional calm

interface IconProps {
  size?: number
  color?: string
  strokeWidth?: number
  className?: string
}

export const ZenIcons = {
  // View action - Simple outline eye (clean, minimal eye shape)
  view: ({ size = 16, color = 'currentColor', strokeWidth = 1.5, className }: IconProps = {}) => (
    <svg
      width={size}
      height={size}
      viewBox="0 0 24 24"
      fill="none"
      stroke={color}
      strokeWidth={strokeWidth}
      className={className}
    >
      <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"/>
      <circle cx="12" cy="12" r="3"/>
    </svg>
  ),

  // Edit action - Simple outline edit/pencil (clean, minimal edit shape)
  edit: ({ size = 16, color = 'currentColor', strokeWidth = 1.5, className }: IconProps = {}) => (
    <svg
      width={size}
      height={size}
      viewBox="0 0 24 24"
      fill="none"
      stroke={color}
      strokeWidth={strokeWidth}
      className={className}
    >
      <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"/>
      <path d="M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z"/>
    </svg>
  ),
  
  // Alternative view - Simple circle (gentle focus/attention)
  viewCircle: ({ size = 16, color = 'currentColor', strokeWidth = 1.5, className }: IconProps = {}) => (
    <svg
      width={size}
      height={size}
      viewBox="0 0 24 24"
      fill="none"
      stroke={color}
      strokeWidth={strokeWidth}
      className={className}
    >
      <circle cx="12" cy="12" r="2"/>
    </svg>
  ),

  // Alternative view - Two gentle lines (abstract representation of reading/viewing content)
  viewLines: ({ size = 16, color = 'currentColor', strokeWidth = 1.5, className }: IconProps = {}) => (
    <svg
      width={size}
      height={size}
      viewBox="0 0 24 24"
      fill="none"
      stroke={color}
      strokeWidth={strokeWidth}
      className={className}
    >
      <line x1="8" y1="10" x2="16" y2="10"/>
      <line x1="8" y1="14" x2="16" y2="14"/>
    </svg>
  ),
  
  // Alternative view - Minimal arrow (suggests "reveal" or "open" - very subtle)
  viewArrow: ({ size = 16, color = 'currentColor', strokeWidth = 1.5, className }: IconProps = {}) => (
    <svg 
      width={size} 
      height={size} 
      viewBox="0 0 24 24" 
      fill="none" 
      stroke={color} 
      strokeWidth={strokeWidth}
      className={className}
    >
      <path d="M7 12l5-5 5 5"/>
    </svg>
  ),
  
  // Alternative edit - Simple corner (abstract geometric shape suggesting modification)
  editCorner: ({ size = 16, color = 'currentColor', strokeWidth = 1.5, className }: IconProps = {}) => (
    <svg
      width={size}
      height={size}
      viewBox="0 0 24 24"
      fill="none"
      stroke={color}
      strokeWidth={strokeWidth}
      className={className}
    >
      <path d="M16 3l5 5-10 10H6v-5L16 3z"/>
    </svg>
  ),

  // Alternative edit - Minimal plus (universal symbol for "add/modify" - very clean)
  editPlus: ({ size = 16, color = 'currentColor', strokeWidth = 1.5, className }: IconProps = {}) => (
    <svg
      width={size}
      height={size}
      viewBox="0 0 24 24"
      fill="none"
      stroke={color}
      strokeWidth={strokeWidth}
      className={className}
    >
      <line x1="12" y1="5" x2="12" y2="19"/>
      <line x1="5" y1="12" x2="19" y2="12"/>
    </svg>
  ),
  
  // Alternative edit - Gentle curve (suggests change/modification in a flowing way)
  editCurve: ({ size = 16, color = 'currentColor', strokeWidth = 1.5, className }: IconProps = {}) => (
    <svg 
      width={size} 
      height={size} 
      viewBox="0 0 24 24" 
      fill="none" 
      stroke={color} 
      strokeWidth={strokeWidth}
      className={className}
    >
      <path d="M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8"/>
    </svg>
  ),
  
  // Delete action - Clean, minimal trash icon consistent with zen style
  delete: ({ size = 16, color = 'currentColor', strokeWidth = 1.5, className }: IconProps = {}) => (
    <svg 
      width={size} 
      height={size} 
      viewBox="0 0 24 24" 
      fill="none" 
      stroke={color} 
      strokeWidth={strokeWidth}
      className={className}
    >
      <polyline points="3,6 5,6 21,6"/>
      <path d="m19,6v14a2,2 0 0,1-2,2H7a2,2 0 0,1-2-2V6m3,0V4a2,2 0 0,1,2-2h4a2,2 0 0,1,2,2v2"/>
    </svg>
  ),
  
  // Additional zen icons for common actions
  
  // Upload - Simple upward arrow
  upload: ({ size = 16, color = 'currentColor', strokeWidth = 1.5, className }: IconProps = {}) => (
    <svg 
      width={size} 
      height={size} 
      viewBox="0 0 24 24" 
      fill="none" 
      stroke={color} 
      strokeWidth={strokeWidth}
      className={className}
    >
      <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"/>
      <polyline points="7,10 12,5 17,10"/>
      <line x1="12" y1="5" x2="12" y2="15"/>
    </svg>
  ),
  
  // Download - Simple downward arrow
  download: ({ size = 16, color = 'currentColor', strokeWidth = 1.5, className }: IconProps = {}) => (
    <svg 
      width={size} 
      height={size} 
      viewBox="0 0 24 24" 
      fill="none" 
      stroke={color} 
      strokeWidth={strokeWidth}
      className={className}
    >
      <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"/>
      <polyline points="7,10 12,15 17,10"/>
      <line x1="12" y1="15" x2="12" y2="3"/>
    </svg>
  ),
  
  // Search - Simple circle with line
  search: ({ size = 16, color = 'currentColor', strokeWidth = 1.5, className }: IconProps = {}) => (
    <svg 
      width={size} 
      height={size} 
      viewBox="0 0 24 24" 
      fill="none" 
      stroke={color} 
      strokeWidth={strokeWidth}
      className={className}
    >
      <circle cx="11" cy="11" r="8"/>
      <path d="m21 21-4.35-4.35"/>
    </svg>
  ),
  
  // Settings - Simple gear
  settings: ({ size = 16, color = 'currentColor', strokeWidth = 1.5, className }: IconProps = {}) => (
    <svg 
      width={size} 
      height={size} 
      viewBox="0 0 24 24" 
      fill="none" 
      stroke={color} 
      strokeWidth={strokeWidth}
      className={className}
    >
      <circle cx="12" cy="12" r="3"/>
      <path d="M12 1v6m0 6v6m11-7h-6m-6 0H1m15.5-3.5L19 9.5m-14 5L7.5 16.5m11-11L16.5 7.5m-9 9L5.5 18.5"/>
    </svg>
  ),
  
  // Close - Simple X
  close: ({ size = 16, color = 'currentColor', strokeWidth = 1.5, className }: IconProps = {}) => (
    <svg
      width={size}
      height={size}
      viewBox="0 0 24 24"
      fill="none"
      stroke={color}
      strokeWidth={strokeWidth}
      className={className}
    >
      <line x1="18" y1="6" x2="6" y2="18"/>
      <line x1="6" y1="6" x2="18" y2="18"/>
    </svg>
  ),

  // Review/Check - Simple checkmark for review actions
  review: ({ size = 16, color = 'currentColor', strokeWidth = 1.5, className }: IconProps = {}) => (
    <svg
      width={size}
      height={size}
      viewBox="0 0 24 24"
      fill="none"
      stroke={color}
      strokeWidth={strokeWidth}
      className={className}
    >
      <polyline points="20,6 9,17 4,12"/>
    </svg>
  ),
}

// Export individual icons for convenience
export const {
  view: ViewIcon,
  edit: EditIcon,
  viewCircle: ViewCircleIcon,
  viewLines: ViewLinesIcon,
  viewArrow: ViewArrowIcon,
  editCorner: EditCornerIcon,
  editPlus: EditPlusIcon,
  editCurve: EditCurveIcon,
  delete: DeleteIcon,
  upload: UploadIcon,
  download: DownloadIcon,
  search: SearchIcon,
  settings: SettingsIcon,
  close: CloseIcon,
  review: ReviewIcon,
} = ZenIcons

export default ZenIcons
